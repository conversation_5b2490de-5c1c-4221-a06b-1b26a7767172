
use iroh::<PERSON><PERSON><PERSON>;
use sha2::{Sha256, Digest};
use ed25519_dalek::{<PERSON>ing<PERSON><PERSON>, Verifying<PERSON><PERSON>};
use curve25519_dalek::{EdwardsP<PERSON>, Scalar};


fn main() {
    // Test with iroh SecretKey
    let secret_key = SecretKey::generate(rand::rngs::ThreadRng::default());
    let query = "example_query";

    // Convert iroh SecretKey to bytes for derivation
    let master_key_bytes = secret_key.to_bytes();
    let master_public = secret_key.public();
    let master_public_bytes = *master_public.as_bytes();

    println!("Master secret: {:?}", master_key_bytes);
    println!("Master public: {:?}", master_public_bytes);

    let derived_keypair = derive_private_keypair(&master_key_bytes, query);
    let derived_public = derive_public_key(&master_key_bytes, &master_public_bytes, query);

    println!("Derived private key public: {:?}", derived_keypair.verifying_key().to_bytes());
    println!("Derived public key: {:?}", derived_public.to_bytes());

    // Compare the derived public keys
    assert_eq!(derived_keypair.verifying_key().to_bytes(), derived_public.to_bytes());
    println!("Key derivation successful!");
}

/// Derives a new keypair from your master secret and a public query.
/// This uses a simple hash-based derivation scheme.
pub fn derive_private_keypair(
    master_secret_bytes: &[u8; 32],
    query: &str,
) -> SigningKey {
    // Create a deterministic derived secret by hashing the master secret with the query
    let mut hasher = Sha256::new();
    hasher.update(b"ed25519_derive_v1");
    hasher.update(master_secret_bytes);
    hasher.update(query.as_bytes());
    let derived_secret_hash = hasher.finalize();

    // Use the first 32 bytes as the new secret key
    let mut derived_secret = [0u8; 32];
    derived_secret.copy_from_slice(&derived_secret_hash[..32]);

    SigningKey::from_bytes(&derived_secret)
}

/// Derives a public key that matches the derived private key.
/// This version takes both the master secret and public key to ensure consistency.
pub fn derive_public_key(
    master_secret_bytes: &[u8; 32],
    _master_public_bytes: &[u8; 32],
    query: &str,
) -> VerifyingKey {
    // For consistency, derive the private key and extract its public key
    // In a real-world scenario, you might use more sophisticated schemes
    // that allow public key derivation without the private key
    let derived_private = derive_private_keypair(master_secret_bytes, query);
    derived_private.verifying_key()
}