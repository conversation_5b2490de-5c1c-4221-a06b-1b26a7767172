[package]
name = "iroh-topic-tracker"
version = "0.1.13"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "Iroh universal (gossipsub) topic tracker."
license = "MIT"
repository = "https://github.com/rustonbsd/iroh-topic-tracker"
readme = "README.md"
keywords = ["gossipsub", "iroh", "pubsub", "networking", "tracker"]
categories = ["network-programming"]

[dependencies]
iroh = "0.90.0"
tokio ={ version="1.45.1",features = ["full"] }
anyhow = "1"
rand_core = { version = "0.6.4" }
rand = "0.8"
serde = "1"
serde_json = "1"
bytes = { version = "1.7", features = ["serde"] }
ed25519-dalek = { version = "2.2.0", features = ["serde", "rand_core"] }
curve25519-dalek = "4.1.3"
postcard = { version = "1.1.2", default-features = false, features = ["alloc", "use-std", "experimental-derive"] }
futures-lite = "2"
z32 = "1"
sha2 = "0.10.9"

# Optional dependencies
iroh-gossip = { version = "0.90.0", optional = true }

[dev-dependencies]
tokio = { version = "1", features = ["full", "test-util"] }

[features]
default = ["iroh-gossip-cast","iroh-gossip-auto-discovery"]
iroh-gossip-cast = ["iroh-gossip"]
iroh-gossip-auto-discovery = ["iroh-gossip","iroh-gossip-cast"]

[profile.dev]
opt-level = 0

[profile.release]
opt-level = 3
lto = true
panic = "abort"